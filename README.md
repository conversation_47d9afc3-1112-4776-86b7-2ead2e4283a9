# Vulkan 3D Graphics Project

A modern C++ project using Vulkan API for 3D graphics rendering, built with xmake.

## Prerequisites

### Required Software
1. **Vulkan SDK** - Download and install from [LunarG](https://vulkan.lunarg.com/)
2. **xmake** - Install from [xmake.io](https://xmake.io/)
3. **C++ Compiler** - MSVC (Windows), GCC/Clang (Linux/macOS)
4. **GLFW** - For window management (install via vcpkg or system package manager)
5. **GLM** - For mathematics (header-only, can be downloaded manually)

### Environment Setup
Make sure the following are in your PATH:
- `glslc` (GLSL to SPIR-V compiler, comes with Vulkan SDK)
- `xmake`

## Building the Project

### 1. Configure the project
```bash
xmake config --mode=debug
```

### 2. Build the project
```bash
xmake build
```

### 3. Run the application
```bash
xmake run vulkan-app
```

## Project Structure

```
vulkan/
├── xmake.lua           # Build configuration
├── src/                # Source code
│   ├── main.cpp        # Entry point
│   ├── VulkanApp.h     # Main application header
│   └── VulkanApp.cpp   # Main application implementation
├── shaders/            # GLSL shader files
│   ├── shader.vert     # Vertex shader
│   └── shader.frag     # Fragment shader
└── build/              # Build output
    ├── vulkan-app      # Executable
    └── shaders/        # Compiled SPIR-V shaders
```

## Features

- **Modern Vulkan API**: Uses Vulkan 1.0+ for high-performance graphics
- **GLFW Integration**: Cross-platform window management
- **GLM Mathematics**: Vector and matrix operations
- **Validation Layers**: Debug support in development builds
- **Shader Compilation**: Automatic GLSL to SPIR-V compilation
- **Cross-platform**: Windows, Linux, and macOS support

## Development

### Debug Mode
```bash
xmake config --mode=debug
xmake build
```

### Release Mode
```bash
xmake config --mode=release
xmake build
```

### Clean Build
```bash
xmake clean
xmake build
```

## Troubleshooting

### Common Issues

1. **Vulkan SDK not found**
   - Ensure Vulkan SDK is properly installed
   - Check that `VULKAN_SDK` environment variable is set

2. **glslc not found**
   - Make sure `glslc` is in your PATH
   - Usually located in `$VULKAN_SDK/bin/`

3. **Validation layers not available**
   - Install Vulkan SDK with validation layers
   - On Linux, install `vulkan-validationlayers` package

### GPU Compatibility
- Requires a Vulkan-compatible GPU
- Check GPU support at [gpuinfo.org](https://vulkan.gpuinfo.org/)

## Next Steps

This is a basic Vulkan setup. You can extend it by:
- Adding vertex buffers and index buffers
- Implementing 3D transformations with uniform buffers
- Adding texture support
- Implementing lighting models
- Adding model loading capabilities

## Resources

- [Vulkan Tutorial](https://vulkan-tutorial.com/)
- [Vulkan Specification](https://www.khronos.org/vulkan/)
- [xmake Documentation](https://xmake.io/#/getting_started)
