-- xmake.lua for Vulkan 3D Graphics Project

set_project("vulkan-3d-graphics")
set_version("1.0.0")

-- Set C++ standard
set_languages("c++17")

-- Add build modes
add_rules("mode.debug", "mode.release")

-- Configure debug mode
if is_mode("debug") then
    set_symbols("debug")
    set_optimize("none")
    add_defines("DEBUG")
end

-- Configure release mode
if is_mode("release") then
    set_symbols("hidden")
    set_optimize("fastest")
    add_defines("NDEBUG")
end

-- Dependencies will be handled manually for better compatibility

-- Main executable target
target("vulkan-app")
    set_kind("binary")
    
    -- Source files
    add_files("src/*.cpp")
    add_headerfiles("src/*.h", "src/*.hpp")
    
    -- Include directories
    add_includedirs("src")
    
    -- Platform-specific setup
    if is_plat("windows") then
        -- Windows: Use system libraries and Vulkan SDK
        add_syslinks("vulkan-1", "glfw3", "user32", "gdi32", "shell32")

        -- Add common include paths for Windows
        add_includedirs("C:/VulkanSDK/*/Include", "C:/vcpkg/installed/x64-windows/include")
        add_linkdirs("C:/VulkanSDK/*/Lib", "C:/vcpkg/installed/x64-windows/lib")

        -- Try environment variables
        if os.getenv("VULKAN_SDK") then
            add_includedirs("$(env VULKAN_SDK)/Include")
            add_linkdirs("$(env VULKAN_SDK)/Lib")
        end

    elseif is_plat("linux") then
        -- Linux: Use system packages
        add_syslinks("vulkan", "glfw", "pthread", "dl", "X11", "Xxf86vm", "Xrandr", "Xi")
        add_includedirs("/usr/include/vulkan")

    elseif is_plat("macosx") then
        -- macOS: Use frameworks
        add_frameworks("Vulkan")
        add_syslinks("glfw")
    end
    

    
    -- Compiler flags
    add_cxxflags("-Wall", "-Wextra")
    
    -- Set output directory
    set_targetdir("build")
target_end()

-- Shader compilation target (optional)
target("shaders")
    set_kind("phony")
    
    -- Custom rule to compile GLSL shaders to SPIR-V
    on_build(function (target)
        import("core.project.config")
        
        local glslc = "glslc"  -- Assumes glslc is in PATH
        local shader_dir = "shaders"
        local output_dir = "build/shaders"
        
        -- Create output directory
        os.mkdir(output_dir)
        
        -- Find all shader files
        local shader_files = os.files(shader_dir .. "/*.vert", shader_dir .. "/*.frag", 
                                     shader_dir .. "/*.comp", shader_dir .. "/*.geom", 
                                     shader_dir .. "/*.tesc", shader_dir .. "/*.tese")
        
        for _, shader_file in ipairs(shader_files) do
            local output_file = output_dir .. "/" .. path.basename(shader_file) .. ".spv"
            print("Compiling shader: " .. shader_file .. " -> " .. output_file)
            os.execv(glslc, {shader_file, "-o", output_file})
        end
    end)
target_end()

-- Add dependency from main target to shaders
add_deps("shaders", "vulkan-app")
