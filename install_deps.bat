@echo off
echo Installing dependencies for Vulkan 3D Graphics project...

echo.
echo 1. Installing vcpkg (if not already installed)...
if not exist "C:\vcpkg" (
    echo Cloning vcpkg...
    git clone https://github.com/Microsoft/vcpkg.git C:\vcpkg
    cd C:\vcpkg
    call bootstrap-vcpkg.bat
    vcpkg integrate install
) else (
    echo vcpkg already installed.
)

echo.
echo 2. Installing GLFW and GLM via vcpkg...
C:\vcpkg\vcpkg install glfw3:x64-windows glm:x64-windows

echo.
echo 3. Please ensure Vulkan SDK is installed from:
echo    https://vulkan.lunarg.com/
echo.
echo 4. Please ensure xmake is installed from:
echo    https://xmake.io/
echo.

echo Dependencies installation complete!
echo You can now run: xmake config --mode=debug
pause
